let currentStep = 0;
let cells = [];
let opacityLevels = [];
let sortedOpacities = [];
let gameCompleted = false;

function generateOpacityLevels() {
    // 生成16个不同的透明度等级，从100%到4%，每级相差约6%
    const levels = [];
    for (let i = 0; i < 16; i++) {
        const opacity = Math.round(100 - i * 6.4);
        levels.push(Math.max(4, opacity)); // 确保最小值为4%
    }
    return levels;
}

function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

function createGrid() {
    const container = document.getElementById('grid-container');
    container.innerHTML = '';

    // 生成透明度等级并打乱
    const baseOpacities = generateOpacityLevels();
    const shuffledOpacities = shuffleArray(baseOpacities);

    // 保存排序后的透明度用于比较
    sortedOpacities = [...baseOpacities].sort((a, b) => b - a);

    opacityLevels = [];
    cells = [];

    for (let i = 0; i < 16; i++) {
        const cell = document.createElement('div');
        cell.className = 'grid-cell';
        cell.dataset.index = i;

        const opacity = shuffledOpacities[i];
        const opacityValue = opacity / 100;

        cell.style.backgroundColor = `rgba(255, 255, 255, ${opacityValue})`;
        cell.dataset.opacity = opacity;

        // 为了调试，可以显示透明度值（可选）
        // cell.textContent = opacity + '%';

        cell.addEventListener('click', handleCellClick);

        container.appendChild(cell);
        cells.push(cell);
        opacityLevels.push(opacity);
    }
}

function findNextExpectedOpacity() {
    // 找到当前应该点击的透明度等级
    // 从排序后的透明度数组中获取
    return sortedOpacities[currentStep];
}

function handleCellClick(event) {
    if (gameCompleted) return;
    
    const cell = event.currentTarget;
    const cellOpacity = parseInt(cell.dataset.opacity);
    const expectedOpacity = findNextExpectedOpacity();
    
    if (cellOpacity === expectedOpacity) {
        // 正确点击
        cell.classList.add('clicked');
        cell.removeEventListener('click', handleCellClick);
        
        currentStep++;
        document.getElementById('current-step').textContent = currentStep;
        
        if (currentStep >= 16) {
            // 游戏完成
            gameCompleted = true;
            document.getElementById('instruction-text').textContent = '恭喜完成！';
            document.querySelector('.instruction').classList.add('success');
            
            setTimeout(() => {
                alert('恭喜你完成第二十三关！恭喜你完成所有关卡！');
            }, 1000);
        } else {
            // 继续下一步
            const nextOpacity = findNextExpectedOpacity();
            document.getElementById('instruction-text').textContent = 
                `点击透明度为 ${nextOpacity}% 的格子`;
        }
    } else {
        // 错误点击
        showError(cell);
    }
}

function showError(cell) {
    cell.classList.add('error');
    document.getElementById('instruction-text').textContent = '点击错误！游戏重新开始';
    document.querySelector('.instruction').classList.add('error');
    
    // 禁用所有点击
    cells.forEach(c => c.style.pointerEvents = 'none');
    
    setTimeout(() => {
        cell.classList.remove('error');
        document.querySelector('.instruction').classList.remove('error');
        resetGame();
    }, 2000);
}

function resetGame() {
    currentStep = 0;
    gameCompleted = false;
    document.getElementById('current-step').textContent = '0';
    document.querySelector('.instruction').classList.remove('success', 'error');

    // 重新生成网格
    createGrid();

    // 显示第一步的提示
    setTimeout(() => {
        const firstOpacity = findNextExpectedOpacity();
        document.getElementById('instruction-text').textContent =
            `点击透明度为 ${firstOpacity}% 的格子`;
    }, 100);
}

// 游戏开始时创建网格
createGrid();
