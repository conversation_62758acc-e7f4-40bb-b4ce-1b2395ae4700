let questionsCompleted = 0;
let speeds = [];
let fastestIndex = 0;
let isAnimating = false;

function generateSpeeds() {
    // 生成三个不同的速度（2-6秒）
    const baseSpeed = 2000;
    const speedRange = 4000;
    speeds = new Set();
    
    // 生成第一个速度
    const firstSpeed = baseSpeed + Math.floor(Math.random() * speedRange);
    speeds.add(firstSpeed);
    
    // 生成第二个速度，确保与第一个速度差至少500ms
    let secondSpeed;
    do {
        secondSpeed = baseSpeed + Math.floor(Math.random() * speedRange);
    } while (Math.abs(secondSpeed - firstSpeed) < 500);
    speeds.add(secondSpeed);
    
    // 生成第三个速度，确保与前两个速度差都至少500ms
    let thirdSpeed;
    do {
        thirdSpeed = baseSpeed + Math.floor(Math.random() * speedRange);
    } while (Math.abs(thirdSpeed - firstSpeed) < 500 || 
             Math.abs(thirdSpeed - secondSpeed) < 500);
    speeds.add(thirdSpeed);
    
    speeds = Array.from(speeds);
    
    // 找出最快的进度条（时间最短的）
    fastestIndex = speeds.indexOf(Math.min(...speeds));
}

function resetBars() {
    const bars = document.querySelectorAll('.bar-fill');
    bars.forEach(bar => {
        // 移除 transition 以立即重置
        bar.style.transition = 'none';
        bar.style.width = '0';
        // 强制浏览器重绘
        bar.offsetHeight;
    });
}

function animateBars() {
    isAnimating = true;
    document.getElementById('replay').disabled = true;
    resetBars();
    
    // 依次播放每个进度条的动画
    const bars = document.querySelectorAll('.bar-fill');
    let currentBar = 0;
    
    function animateNext() {
        if (currentBar >= bars.length) {
            isAnimating = false;
            document.getElementById('replay').disabled = false;
            return;
        }
        
        const bar = bars[currentBar];
        const duration = speeds[currentBar];
        
        // 重新添加 transition 并开始动画
        requestAnimationFrame(() => {
            bar.style.transition = `width ${duration}ms linear`;
            bar.style.width = '100%';
        });
        
        // 等待当前动画完成后开始下一个
        setTimeout(() => {
            currentBar++;
            animateNext();
        }, duration + 500);
    }
    
    // 确保重置后再开始动画
    requestAnimationFrame(animateNext);
}

function showError(element) {
    // 获取整个进度条元素（父元素）
    const progressBar = element.closest('.progress-bar');
    progressBar.classList.add('error');
    setTimeout(() => {
        progressBar.classList.remove('error');
        resetGame();
    }, 1000);
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateQuestion() {
    generateSpeeds();
    resetBars();
    setTimeout(animateBars, 500);
}

// 为每个进度条添加点击事件
document.querySelectorAll('.progress-bar').forEach((bar, index) => {
    bar.addEventListener('click', () => {
        if (isAnimating) return;
        
        if (index === fastestIndex) {
            questionsCompleted++;
            document.getElementById('progress').textContent = questionsCompleted;
            
            if (questionsCompleted >= 6) {
                alert('恭喜你完成第十四关！');
                window.location.href = '../level15/index.html';
                return;
            }
            
            generateQuestion();
        } else {
            showError(bar);  // 直接传入进度条元素
        }
    });
});

// 重放按钮事件
document.getElementById('replay').addEventListener('click', () => {
    if (!isAnimating) {
        animateBars();
    }
});

// 游戏开始时生成第一个问题
generateQuestion(); 