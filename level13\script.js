let questionsCompleted = 0;
let timer = null;
let timeLeft = 8;
let numbers = [];
let minValue = 0;
let maxValue = 0;
let selectedMin = false;
let selectedMax = false;

function startTimer() {
    clearInterval(timer);
    timeLeft = 8;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomNumber() {
    return Math.floor(Math.random() * 9000) + 1000; // 生成1000-9999的随机数
}

function generateQuestion() {
    const container = document.getElementById('grid-container');
    container.innerHTML = '';
    document.getElementById('status').textContent = '请选择最小值';
    
    // 生成9个不重复的4位数
    numbers = new Set();
    while (numbers.size < 9) {
        numbers.add(generateRandomNumber());
    }
    numbers = Array.from(numbers);
    
    // 找出最大值和最小值
    minValue = Math.min(...numbers);
    maxValue = Math.max(...numbers);
    
    // 重置选择状态
    selectedMin = false;
    selectedMax = false;
    
    // 创建网格
    numbers.forEach((num, index) => {
        const cell = document.createElement('div');
        cell.className = 'number-cell';
        cell.textContent = num;
        cell.dataset.index = index;
        cell.dataset.value = num;
        
        cell.addEventListener('click', handleCellClick);
        container.appendChild(cell);
    });
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleCellClick(event) {
    const cell = event.target;
    const value = parseInt(cell.dataset.value);
    
    if (!selectedMin) {
        // 选择最小值
        if (value === minValue) {
            cell.classList.add('selected');
            selectedMin = true;
            document.getElementById('status').textContent = '请选择最大值';
        } else {
            showError(cell);
        }
    } else if (!selectedMax) {
        // 选择最大值
        if (value === maxValue) {
            cell.classList.add('selected');
            selectedMax = true;
            clearInterval(timer);
            
            questionsCompleted++;
            document.getElementById('progress').textContent = questionsCompleted;
            
            if (questionsCompleted >= 10) {
                alert('恭喜你完成第十三关！');
                window.location.href = '../level14/index.html';
                return;
            }
            
            setTimeout(generateQuestion, 500);
        } else {
            showError(cell);
        }
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 