let questionsCompleted = 0;
let correctIndex = 0;

function generateRandomDimension(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function generateDifferentDimension(original, minDiff, maxDiff) {
    // 随机决定是增加还是减少
    const isIncrease = Math.random() < 0.5;
    const diff = Math.floor(Math.random() * (maxDiff - minDiff + 1)) + minDiff;
    return isIncrease ? original + diff : Math.max(20, original - diff);
}

function generateQuestion() {
    const container = document.getElementById('rectangles-container');
    container.innerHTML = '';
    
    // 生成目标矩形的尺寸（20-80px）
    const targetWidth = generateRandomDimension(20, 80);
    const targetHeight = generateRandomDimension(20, 80);
    
    // 设置目标矩形
    const targetRect = document.getElementById('target-rect');
    targetRect.style.width = `${targetWidth}px`;
    targetRect.style.height = `${targetHeight}px`;
    
    // 随机选择正确答案的位置
    correctIndex = Math.floor(Math.random() * 4);
    
    // 移除所有已选中状态
    document.querySelectorAll('.rect.selected').forEach(rect => {
        rect.classList.remove('selected');
    });
    
    // 生成4个矩形
    for (let i = 0; i < 4; i++) {
        let width, height;
        
        if (i === correctIndex) {
            // 正确答案使用相同尺寸
            width = targetWidth;
            height = targetHeight;
        } else {
            // 随机决定改变宽度还是高度
            if (Math.random() < 0.5) {
                width = generateDifferentDimension(targetWidth, 5, 20);
                height = targetHeight;
            } else {
                width = targetWidth;
                height = generateDifferentDimension(targetHeight, 5, 20);
            }
        }
        
        const rect = document.createElement('div');
        rect.className = 'rect';
        rect.style.width = `${width}px`;
        rect.style.height = `${height}px`;
        rect.dataset.index = i;
        
        rect.addEventListener('click', handleRectClick);
        container.appendChild(rect);
    }
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function handleRectClick(event) {
    const index = parseInt(event.target.dataset.index);
    
    // 移除其他矩形的选中状态
    document.querySelectorAll('.rect.selected').forEach(rect => {
        rect.classList.remove('selected');
    });
    
    // 添加当前矩形的选中状态
    event.target.classList.add('selected');
    
    if (index === correctIndex) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第十五关！');
            window.location.href = '../level16/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError(event.target);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 