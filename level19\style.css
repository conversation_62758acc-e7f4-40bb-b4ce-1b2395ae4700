body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.target-time {
    font-size: 32px;
    font-family: monospace;
    margin: 20px 0;
}

.clocks-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 20px auto;
    max-width: 500px;
}

.clock {
    width: 120px;
    height: 120px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    position: relative;
    cursor: pointer;
    margin: 0 auto;
    transition: all 0.3s;
}

.clock:hover {
    border-color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.clock.selected {
    border-color: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.clock.error {
    border-color: red;
    animation: shake 0.5s;
}

.hand {
    position: absolute;
    left: 50%;
    bottom: 50%;
    transform-origin: bottom;
    background-color: white;
}

.hour-hand {
    width: 4px;
    height: 30px;
    margin-left: -2px;
}

.minute-hand {
    width: 2px;
    height: 45px;
    margin-left: -1px;
}

.center-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 