let questionsCompleted = 0;
let memoryTimer = null;
let timeLeft = 6;
let numbers = [];
let highlightedCells = [];
let correctAnswer = 0;

function startMemoryTimer() {
    clearInterval(memoryTimer);
    timeLeft = 6;
    updateTimerDisplay();
    
    memoryTimer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(memoryTimer);
            hideNumbersAndShowHighlights();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('memory-timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateNumbers() {
    // 生成1-9的随机排列
    const nums = Array.from({length: 9}, (_, i) => i + 1);
    for (let i = nums.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [nums[i], nums[j]] = [nums[j], nums[i]];
    }
    return nums;
}

function generateHighlightedCells() {
    // 随机选择1-2个格子
    const count = Math.floor(Math.random() * 2) + 1;
    const cells = Array.from({length: 9}, (_, i) => i);
    const selected = [];
    
    for (let i = 0; i < count; i++) {
        const index = Math.floor(Math.random() * cells.length);
        selected.push(cells[index]);
        cells.splice(index, 1);
    }
    
    return selected;
}

function generateQuestion() {
    const grid = document.getElementById('number-grid');
    grid.innerHTML = '';
    document.querySelector('.memory-phase').classList.remove('hidden');
    document.getElementById('answer-section').classList.add('hidden');
    
    numbers = generateNumbers();
    highlightedCells = generateHighlightedCells();
    
    // 计算正确答案
    correctAnswer = highlightedCells.reduce((sum, index) => sum + numbers[index], 0);
    
    // 创建网格
    numbers.forEach((num, index) => {
        const cell = document.createElement('div');
        cell.className = 'grid-cell';
        cell.textContent = num;
        grid.appendChild(cell);
    });
    
    // 清空输入框
    const input = document.getElementById('answer');
    input.value = '';
    input.classList.remove('error');
    
    startMemoryTimer();
}

function hideNumbersAndShowHighlights() {
    document.querySelector('.memory-phase').classList.add('hidden');
    const cells = document.querySelectorAll('.grid-cell');
    
    cells.forEach((cell, index) => {
        cell.textContent = '';
        if (highlightedCells.includes(index)) {
            cell.classList.add('highlighted');
        }
    });
    
    document.getElementById('answer-section').classList.remove('hidden');
    document.getElementById('answer').focus();
}

function showError() {
    const input = document.getElementById('answer');
    input.classList.add('error');
    setTimeout(() => {
        input.classList.remove('error');
        resetGame();
    }, 1000);
}

function checkAnswer() {
    const input = document.getElementById('answer');
    const userAnswer = parseInt(input.value);
    
    if (userAnswer === correctAnswer) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第十关！');
            window.location.href = '../level11/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError();
    }
}

// 添加回车键监听
document.getElementById('answer').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        checkAnswer();
    }
});

// 游戏开始时生成第一个问题
generateQuestion(); 