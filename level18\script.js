let questionsCompleted = 0;
let timer = null;
let timeLeft = 10;
let correctGroupIndex = 0;
let groupSums = [];

function startTimer() {
    clearInterval(timer);
    timeLeft = 10;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomNumber() {
    return Math.floor(Math.random() * 90) + 10; // 生成10-99的随机数
}

function generateQuestion() {
    const container = document.getElementById('numbers-container');
    container.innerHTML = '';
    groupSums = [];
    
    // 生成6组数字（而不是4组）
    for (let i = 0; i < 6; i++) {
        const group = document.createElement('div');
        group.className = 'number-group';
        group.dataset.index = i;
        
        // 生成3个随机数
        const numbers = [
            generateRandomNumber(),
            generateRandomNumber(),
            generateRandomNumber()
        ];
        
        // 计算和
        const sum = numbers.reduce((a, b) => a + b, 0);
        groupSums.push(sum);
        
        // 创建数字元素
        numbers.forEach(num => {
            const numElement = document.createElement('div');
            numElement.className = 'number';
            numElement.textContent = num;
            group.appendChild(numElement);
        });
        
        group.addEventListener('click', handleGroupClick);
        container.appendChild(group);
    }
    
    // 找出最大和的索引
    correctGroupIndex = groupSums.indexOf(Math.max(...groupSums));
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleGroupClick(event) {
    const group = event.currentTarget;
    const index = parseInt(group.dataset.index);
    
    // 移除其他组的选中状态
    document.querySelectorAll('.number-group.selected').forEach(g => {
        g.classList.remove('selected');
    });
    
    // 添加当前组的选中状态
    group.classList.add('selected');
    
    if (index === correctGroupIndex) {
        clearInterval(timer);
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 8) {
            alert('恭喜你完成第十八关！');
            window.location.href = '../level19/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        clearInterval(timer);
        showError(group);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 