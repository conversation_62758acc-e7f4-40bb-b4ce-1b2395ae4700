let questionsCompleted = 0;
let timer = null;
let timeLeft = 12;
let currentArrowIndex = 0;
let arrows = [];

const arrowTypes = {
    '→': 'ArrowLeft',
    '←': 'ArrowRight',
    '↑': 'ArrowDown',
    '↓': 'ArrowUp'
};

function startTimer() {
    clearInterval(timer);
    timeLeft = 12;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateArrows() {
    // 生成5-10个随机箭头
    const count = Math.floor(Math.random() * 6) + 5;
    const arrowSymbols = Object.keys(arrowTypes);
    const generatedArrows = [];
    
    for (let i = 0; i < count; i++) {
        const arrow = arrowSymbols[Math.floor(Math.random() * arrowSymbols.length)];
        generatedArrows.push(arrow);
    }
    
    return generatedArrows;
}

function generateQuestion() {
    const container = document.getElementById('arrows-container');
    container.innerHTML = '';
    currentArrowIndex = 0;
    arrows = generateArrows();
    
    arrows.forEach((arrow, index) => {
        const element = document.createElement('div');
        element.className = 'arrow' + (index === 0 ? ' current' : '');
        element.textContent = arrow;
        container.appendChild(element);
    });
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleKeyPress(event) {
    if (currentArrowIndex >= arrows.length) return;
    
    const currentArrow = arrows[currentArrowIndex];
    const expectedKey = arrowTypes[currentArrow];
    const arrowElements = document.querySelectorAll('.arrow');
    
    if (event.key === expectedKey) {
        // 正确按键
        arrowElements[currentArrowIndex].style.visibility = 'hidden';
        currentArrowIndex++;
        
        if (currentArrowIndex < arrows.length) {
            arrowElements[currentArrowIndex].classList.add('current');
        } else {
            // 完成当前组
            clearInterval(timer);
            questionsCompleted++;
            document.getElementById('progress').textContent = questionsCompleted;
            
            if (questionsCompleted >= 10) {
                alert('恭喜你完成第八关！');
                window.location.href = '../level9/index.html';
                return;
            }
            
            generateQuestion();
        }
    } else if (Object.values(arrowTypes).includes(event.key)) {
        // 错误按键
        showError(arrowElements[currentArrowIndex]);
    }
}

// 添加键盘事件监听
document.addEventListener('keydown', handleKeyPress);

// 游戏开始时生成第一个问题
generateQuestion(); 