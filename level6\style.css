body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.shapes-container {
    width: 460px;
    height: 320px;
    margin: 15px auto;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.shape {
    position: absolute;
    text-align: center;
    user-select: none;
    font-size: 20px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    cursor: pointer;
    transition: all 0.2s;
}

.shape:hover {
    transform: scale(1.1);
}

.shape.error {
    color: red;
    animation: shake 0.5s;
}

.target-shape {
    font-size: 24px;
    vertical-align: middle;
}

.target-info {
    font-size: 16px;
    margin: 8px 0;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 