body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.shapes-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 20px;
    margin: 20px auto;
    width: 460px;
    height: 320px;
}

.shape-group {
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s;
    padding: 10px;
}

.shape-group:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.shape-group.error {
    border-color: red;
    background-color: rgba(255, 0, 0, 0.1);
}

.shapes-container {
    width: 200px;
    height: 140px;
    position: relative;
    margin: 0 auto;
}

.shape {
    position: absolute;
    text-align: center;
    user-select: none;
}

.target-number {
    font-size: 24px;
    margin: 10px 0;
}

.timer, .progress {
    font-size: 18px;
    margin: 10px 0;
} 