let correctAnswer = 0;
let questionsCompleted = 0;
let memoryTimer = null;
let timeLeft = 10;

const shapes = ['⚪', '▲', '■', '★', '♦', '♥'];

function startMemoryTimer() {
    clearInterval(memoryTimer);
    timeLeft = 10;
    updateTimerDisplay();
    
    memoryTimer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(memoryTimer);
            hideShapes();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('memory-timer').textContent = timeLeft;
}

function hideShapes() {
    document.getElementById('shapes-container').style.visibility = 'hidden';
    document.querySelector('.memory-phase').classList.add('hidden');
    document.getElementById('answer-section').classList.remove('hidden');
    document.getElementById('answer').focus();
}

function showShapes() {
    document.getElementById('shapes-container').style.visibility = 'visible';
    document.querySelector('.memory-phase').classList.remove('hidden');
    document.getElementById('answer-section').classList.add('hidden');
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function isOverlapping(rect1, rect2) {
    return !(rect1.x + rect1.width <= rect2.x ||
             rect1.x >= rect2.x + rect2.width ||
             rect1.y + rect1.height <= rect2.y ||
             rect1.y >= rect2.y + rect2.height);
}

function generateRandomPosition(size) {
    const container = document.getElementById('shapes-container');
    const maxX = container.offsetWidth - size;
    const maxY = container.offsetHeight - size;
    const maxAttempts = 50;
    const existingShapes = Array.from(container.children).map(child => {
        return {
            x: parseInt(child.style.left),
            y: parseInt(child.style.top),
            width: size,
            height: size
        };
    });

    for (let i = 0; i < maxAttempts; i++) {
        const x = Math.floor(Math.random() * maxX);
        const y = Math.floor(Math.random() * maxY);
        const newRect = { x, y, width: size, height: size };
        
        let hasOverlap = false;
        for (const shape of existingShapes) {
            if (isOverlapping(newRect, shape)) {
                hasOverlap = true;
                break;
            }
        }
        
        if (!hasOverlap) {
            return { x, y };
        }
    }
    
    return null;
}

function generateQuestion() {
    const container = document.getElementById('shapes-container');
    container.innerHTML = '';
    showShapes();
    
    const totalShapes = Math.floor(Math.random() * 15) + 5; // 5-20个图形
    correctAnswer = totalShapes;
    let placedShapes = 0;
    
    while (placedShapes < totalShapes) {
        const shape = shapes[Math.floor(Math.random() * shapes.length)];
        const size = 30;
        const element = document.createElement('div');
        element.className = 'shape';
        element.textContent = shape;
        
        const pos = generateRandomPosition(size);
        if (pos === null) break;
        
        element.style.left = pos.x + 'px';
        element.style.top = pos.y + 'px';
        
        container.appendChild(element);
        placedShapes++;
    }
    
    startMemoryTimer();
}

function showError() {
    const input = document.getElementById('answer');
    input.classList.add('error');
    setTimeout(() => {
        input.classList.remove('error');
        input.value = '';
        resetGame();
    }, 1000);
}

function checkAnswer() {
    const input = document.getElementById('answer');
    const userAnswer = parseInt(input.value);
    
    if (userAnswer === correctAnswer) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第五关！');
            window.location.href = '../level6/index.html';
            return;
        }
        
        input.value = '';
        generateQuestion();
    } else {
        showError();
    }
}

// 添加回车键监听
document.getElementById('answer').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        checkAnswer();
    }
});

// 游戏开始时生成第一个问题
generateQuestion(); 