body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.grid-container {
    width: 400px;
    height: 400px;
    margin: 20px auto;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 2px;
}

.grid-cell {
    background-color: rgb(255, 255, 255);
    cursor: pointer;
    transition: all 0.3s;
}

.grid-cell:hover {
    transform: scale(1.1);
    z-index: 1;
}

.grid-cell.error {
    background-color: red;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 