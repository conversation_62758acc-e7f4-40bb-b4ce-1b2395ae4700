let questionsCompleted = 0;
let timer = null;
let timeLeft = 15;
let differentShapeIndex = 0;

function startTimer() {
    clearInterval(timer);
    timeLeft = 15;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomPosition() {
    return {
        x: Math.floor(Math.random() * 235), // 250 - 15(shape width)
        y: Math.floor(Math.random() * 335)  // 350 - 15(shape height)
    };
}

function generateQuestion() {
    const area1 = document.getElementById('area1');
    const area2 = document.getElementById('area2');
    area1.innerHTML = '';
    area2.innerHTML = '';
    
    // 生成50个位置
    const positions = [];
    while (positions.length < 50) {
        const pos = generateRandomPosition();
        // 确保位置不会重叠
        if (!positions.some(p => Math.abs(p.x - pos.x) < 20 && Math.abs(p.y - pos.y) < 20)) {
            positions.push(pos);
        }
    }
    
    // 随机选择一个图形改变位置
    differentShapeIndex = Math.floor(Math.random() * 50);
    let differentPosition;
    do {
        differentPosition = generateRandomPosition();
    } while (positions.some(p => Math.abs(p.x - differentPosition.x) < 20 && 
                                Math.abs(p.y - differentPosition.y) < 20));
    
    // 创建图形
    positions.forEach((pos, index) => {
        const shape1 = document.createElement('div');
        const shape2 = document.createElement('div');
        
        shape1.className = 'shape';
        shape2.className = 'shape';
        
        shape1.style.left = `${pos.x}px`;
        shape1.style.top = `${pos.y}px`;
        
        if (index === differentShapeIndex) {
            shape2.style.left = `${differentPosition.x}px`;
            shape2.style.top = `${differentPosition.y}px`;
        } else {
            shape2.style.left = `${pos.x}px`;
            shape2.style.top = `${pos.y}px`;
        }
        
        shape1.dataset.index = index;
        shape2.dataset.index = index;
        
        shape1.addEventListener('click', handleShapeClick);
        shape2.addEventListener('click', handleShapeClick);
        
        area1.appendChild(shape1);
        area2.appendChild(shape2);
    });
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    const index = parseInt(element.dataset.index);
    
    // 找到另一个区域对应的图形也添加错误效果
    const otherArea = element.parentElement.id === 'area1' ? 'area2' : 'area1';
    const otherShape = document.querySelector(`#${otherArea} .shape[data-index="${index}"]`);
    otherShape.classList.add('error');
    
    setTimeout(() => {
        element.classList.remove('error');
        otherShape.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleShapeClick(event) {
    const index = parseInt(event.target.dataset.index);
    
    if (index === differentShapeIndex) {
        clearInterval(timer);
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第十七关！');
            window.location.href = '../level18/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        clearInterval(timer);
        showError(event.target);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 