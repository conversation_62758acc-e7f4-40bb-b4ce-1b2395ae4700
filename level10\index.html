<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第 10 关 - 记忆求和游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-content">
            <h1>记忆求和游戏</h1>
            <p>记住数字位置，输入高亮格子的数字之和</p>
            <div class="progress">题目进度：<span id="progress">0</span>/10</div>
            <div class="memory-phase">记忆时间：<span id="memory-timer">6</span>秒</div>
            <div class="grid-container">
                <div id="number-grid" class="number-grid">
                    <!-- 数字网格将由JavaScript动态生成 -->
                </div>
            </div>
            <div id="answer-section" class="answer-section hidden">
                <input type="number" id="answer" placeholder="请输入答案">
                <button onclick="checkAnswer()">提交答案</button>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 