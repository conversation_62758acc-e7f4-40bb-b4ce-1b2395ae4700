<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第 18 关 - 数字求和游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-content">
            <h1>数字求和游戏</h1>
            <p>选出和最大的一组数字</p>
            <div class="progress">题目进度：<span id="progress">0</span>/8</div>
            <div class="timer">剩余时间：<span id="timer">10</span>秒</div>
            <div id="numbers-container" class="numbers-container">
                <!-- 数字组将由JavaScript动态生成 -->
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 