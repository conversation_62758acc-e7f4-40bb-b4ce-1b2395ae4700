<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第 9 关 - 数字求和游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-content">
            <h1>数字求和游戏</h1>
            <p>找出和等于目标数字的一组数</p>
            <div class="progress">题目进度：<span id="progress">0</span>/10</div>
            <div class="timer">剩余时间：<span id="timer">15</span>秒</div>
            <div class="target-number">
                目标数字：<span id="target"></span>
            </div>
            <div id="numbers-container" class="numbers-container">
                <!-- 数字组将由JavaScript动态生成 -->
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 