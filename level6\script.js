let questionsCompleted = 0;
let timer = null;
let timeLeft = 12;
let targetShape = '';
let remainingTargets = 0;

const shapes = ['⚪', '▲', '■', '★', '♦', '♥'];

function startTimer() {
    clearInterval(timer);
    timeLeft = 12;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function isOverlapping(rect1, rect2) {
    return !(rect1.x + rect1.width <= rect2.x ||
             rect1.x >= rect2.x + rect2.width ||
             rect1.y + rect1.height <= rect2.y ||
             rect1.y >= rect2.y + rect2.height);
}

function generateRandomPosition(size) {
    const container = document.getElementById('shapes-container');
    const maxX = container.offsetWidth - size;
    const maxY = container.offsetHeight - size;
    const maxAttempts = 50;
    const existingShapes = Array.from(container.children).map(child => {
        return {
            x: parseInt(child.style.left),
            y: parseInt(child.style.top),
            width: size,
            height: size
        };
    });

    for (let i = 0; i < maxAttempts; i++) {
        const x = Math.floor(Math.random() * maxX);
        const y = Math.floor(Math.random() * maxY);
        const newRect = { x, y, width: size, height: size };
        
        let hasOverlap = false;
        for (const shape of existingShapes) {
            if (isOverlapping(newRect, shape)) {
                hasOverlap = true;
                break;
            }
        }
        
        if (!hasOverlap) {
            return { x, y };
        }
    }
    
    return null;
}

function generateQuestion() {
    const container = document.getElementById('shapes-container');
    container.innerHTML = '';
    
    // 选择目标图形
    targetShape = shapes[Math.floor(Math.random() * shapes.length)];
    document.getElementById('target-shape').textContent = targetShape;
    
    // 生成总图形数量（15-40个）
    const totalShapes = Math.floor(Math.random() * 26) + 15;
    
    // 确保至少有5个目标图形，最多12个
    const minTargets = 5;
    const maxTargets = Math.min(12, Math.floor(totalShapes / 2));
    const targetCount = Math.floor(Math.random() * (maxTargets - minTargets + 1)) + minTargets;
    
    remainingTargets = targetCount;
    updateRemainingTargets();
    
    let placedShapes = 0;
    let placedTargets = 0;
    
    while (placedShapes < totalShapes) {
        const shouldBeTarget = placedTargets < targetCount && 
            (Math.random() < 0.5 || totalShapes - placedShapes <= targetCount - placedTargets);
        
        const shape = shouldBeTarget ? targetShape : shapes[Math.floor(Math.random() * shapes.length)];
        if (shape === targetShape && !shouldBeTarget) continue;
        
        const element = document.createElement('div');
        element.className = 'shape';
        element.textContent = shape;
        
        const pos = generateRandomPosition(24);
        if (pos === null) break;
        
        element.style.left = pos.x + 'px';
        element.style.top = pos.y + 'px';
        element.addEventListener('click', handleShapeClick);
        
        container.appendChild(element);
        placedShapes++;
        if (shouldBeTarget) placedTargets++;
    }
    
    startTimer();
}

function updateRemainingTargets() {
    document.getElementById('remaining-targets').textContent = remainingTargets;
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleShapeClick(event) {
    const shape = event.target;
    
    if (shape.textContent === targetShape) {
        shape.remove();
        remainingTargets--;
        updateRemainingTargets();
        
        if (remainingTargets === 0) {
            clearInterval(timer);
            questionsCompleted++;
            document.getElementById('progress').textContent = questionsCompleted;
            
            if (questionsCompleted >= 10) {
                alert('恭喜你完成第六关！');
                window.location.href = '../level7/index.html';
                return;
            }
            
            generateQuestion();
        }
    } else {
        showError(shape);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 