body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.target-container {
    margin: 30px auto;
    text-align: center;
}

.target-label {
    margin-bottom: 10px;
    font-size: 18px;
}

.target-rect {
    margin: 0 auto;
    background-color: white;
}

.rectangles-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
    padding: 0 20px;
}

.rect {
    background-color: white;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    border: 2px solid transparent;
    box-sizing: border-box;
}

.rect:hover {
    border-color: rgba(255, 255, 255, 0.5);
}

.rect.selected {
    border-color: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.rect.error {
    background-color: red;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.progress {
    font-size: 16px;
    margin: 8px 0;
} 