body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.shapes-container {
    width: 500px;
    height: 250px;
    margin: 20px auto;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.shape {
    position: absolute;
    text-align: center;
    user-select: none;
}

input {
    padding: 10px;
    font-size: 18px;
    width: 100px;
    margin: 10px;
    text-align: center;
    background: transparent;
    border: 2px solid white;
    color: white;
    outline: none;
}

input.error {
    border-color: red;
    color: red;
}

button {
    padding: 10px 20px;
    font-size: 18px;
    background: transparent;
    color: white;
    border: 2px solid white;
    cursor: pointer;
    margin: 10px;
    transition: all 0.3s;
}

button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.timer, .progress {
    font-size: 18px;
    margin: 10px 0;
}

.question {
    font-size: 24px;
    margin: 20px 0;
} 