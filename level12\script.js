let questionsCompleted = 0;
let timer = null;
let timeLeft = 8;
let correctIndex = 0;

// 计算每轮的不透明度
function calculateOpacity(round) {
    // 从0.9到0.98，随着轮数递增
    return Math.min(0.98, 0.9 + (round * 0.004));
}

function startTimer() {
    clearInterval(timer);
    timeLeft = 8;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateQuestion() {
    const container = document.getElementById('grid-container');
    container.innerHTML = '';
    
    // 生成100个格子
    correctIndex = Math.floor(Math.random() * 100);
    const targetOpacity = calculateOpacity(questionsCompleted);
    
    for (let i = 0; i < 100; i++) {
        const cell = document.createElement('div');
        cell.className = 'grid-cell';
        cell.dataset.index = i;
        
        // 只给目标格子设置透明度
        if (i === correctIndex) {
            cell.style.backgroundColor = `rgba(255, 255, 255, ${targetOpacity})`;
        }
        // 其他格子使用CSS中定义的纯白色背景
        
        cell.addEventListener('click', handleCellClick);
        container.appendChild(cell);
    }
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleCellClick(event) {
    clearInterval(timer);
    const index = parseInt(event.target.dataset.index);
    
    if (index === correctIndex) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 20) {
            alert('恭喜你完成第十二关！');
            window.location.href = '../level13/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError(event.target);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 