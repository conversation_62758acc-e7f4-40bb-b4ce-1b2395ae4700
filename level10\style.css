body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.grid-container {
    width: 300px;
    height: 300px;
    margin: 20px auto;
}

.number-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    width: 100%;
    height: 100%;
}

.grid-cell {
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36px;
    font-family: monospace;
    transition: all 0.3s;
}

.grid-cell.highlighted {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: white;
}

.answer-section {
    margin-top: 20px;
}

.answer-section.hidden {
    display: none;
}

input {
    padding: 8px;
    font-size: 18px;
    width: 100px;
    margin: 5px;
    text-align: center;
    background: transparent;
    border: 2px solid white;
    color: white;
    outline: none;
}

input.error {
    border-color: red;
    color: red;
    animation: shake 0.5s;
}

button {
    padding: 8px 20px;
    font-size: 18px;
    background: transparent;
    color: white;
    border: 2px solid white;
    cursor: pointer;
    margin: 5px;
    transition: all 0.3s;
}

button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.memory-phase, .progress {
    font-size: 16px;
    margin: 8px 0;
}

.memory-phase.hidden {
    visibility: hidden;
} 