body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.description {
    font-size: 24px;
    margin: 30px 0;
    min-height: 30px;
}

.numbers-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 40px;
}

.number {
    font-size: 36px;
    width: 80px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
}

.number:hover {
    transform: scale(1.1);
}

.number.yellow {
    color: #FFD700;
}

.number.italic {
    text-decoration: underline;
    text-underline-offset: 8px;
}

.number.error {
    border-color: red;
    color: red;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 