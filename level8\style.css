body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.arrows-container {
    width: 460px;
    min-height: 200px;
    margin: 20px auto;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.arrow {
    font-size: 36px;
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s;
}

.arrow.current {
    border-color: white;
}

.arrow.error {
    border-color: red;
    color: red;
    animation: shake 0.5s;
}

.hint {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.hint-item {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 