body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.numbers-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 40px auto;
    max-width: 500px;
}

.number-group {
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.number-group:hover {
    border-color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.number-group.selected {
    border-color: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.number-group.error {
    border-color: red;
    color: red;
    animation: shake 0.5s;
}

.number {
    font-size: 24px;
    font-family: monospace;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 