<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第 5 关 - 记忆计数游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-content">
            <h1>记忆计数游戏</h1>
            <p>记住图形的数量</p>
            <div class="progress">题目进度：<span id="progress">0</span>/10</div>
            <div class="memory-phase">记忆时间：<span id="memory-timer">10</span>秒</div>
            <div id="shapes-container" class="shapes-container">
                <!-- 图形将由JavaScript动态生成 -->
            </div>
            <div id="answer-section" class="answer-section hidden">
                <p>这组图形总共有多少个？</p>
                <input type="number" id="answer" placeholder="请输入数量">
                <button onclick="checkAnswer()">提交答案</button>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 