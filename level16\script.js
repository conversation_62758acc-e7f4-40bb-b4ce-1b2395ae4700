let questionsCompleted = 0;
let timer = null;
let timeLeft = 4;
let missingNumber = 0;

function startTimer() {
    clearInterval(timer);
    timeLeft = 4;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateQuestion() {
    const container = document.getElementById('numbers-container');
    container.innerHTML = '';
    
    // 生成1-9的数组
    const numbers = Array.from({length: 9}, (_, i) => i + 1);
    
    // 随机选择一个数字作为缺失数字
    const randomIndex = Math.floor(Math.random() * 9);
    missingNumber = numbers[randomIndex];
    numbers.splice(randomIndex, 1);
    
    // 打乱剩余数字的顺序
    for (let i = numbers.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numbers[i], numbers[j]] = [numbers[j], numbers[i]];
    }
    
    // 创建数字元素
    numbers.forEach(num => {
        const element = document.createElement('div');
        element.className = 'number';
        element.textContent = num;
        container.appendChild(element);
    });
    
    startTimer();
}

function showError() {
    // 给所有数字添加错误效果
    document.querySelectorAll('.number').forEach(element => {
        element.classList.add('error');
    });
    
    setTimeout(() => {
        // 移除错误效果并重置游戏
        document.querySelectorAll('.number').forEach(element => {
            element.classList.remove('error');
        });
        resetGame();
    }, 1000);
}

function handleKeyPress(event) {
    if (!timer) return; // 如果没有计时器在运行，忽略按键
    
    const pressedNumber = parseInt(event.key);
    if (pressedNumber === missingNumber) {
        clearInterval(timer);
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第十六关！');
            window.location.href = '../level17/index.html';
            return;
        }
        
        generateQuestion();
    } else if (pressedNumber >= 1 && pressedNumber <= 9) {
        // 只有按下1-9的数字键才显示错误效果
        clearInterval(timer);
        showError();
    }
}

// 添加键盘事件监听
document.addEventListener('keydown', handleKeyPress);

// 游戏开始时生成第一个问题
generateQuestion(); 