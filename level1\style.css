body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
}

h1 {
    margin-bottom: 20px;
}

.question {
    font-size: 24px;
    margin: 20px 0;
}

input {
    padding: 10px;
    font-size: 18px;
    width: 100px;
    margin: 10px;
    text-align: center;
    background: transparent;
    border: 2px solid white;
    color: white;
    outline: none;
}

input.error {
    border-color: red;
    color: red;
}

button {
    padding: 10px 20px;
    font-size: 18px;
    background: transparent;
    color: white;
    border: 2px solid white;
    cursor: pointer;
    margin: 10px;
    transition: all 0.3s;
}

button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.timer {
    font-size: 18px;
    margin: 10px 0;
}

.progress {
    margin: 20px 0;
    font-size: 18px;
} 