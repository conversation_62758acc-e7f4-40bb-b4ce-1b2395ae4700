let questionsCompleted = 0;
let timer = null;
let timeLeft = 12;
let target1 = { char: '', count: 0 };
let target2 = { char: '', count: 0 };

const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

function startTimer() {
    clearInterval(timer);
    timeLeft = 12;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            checkAnswer();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomLetter() {
    return letters[Math.floor(Math.random() * letters.length)];
}

function generateRandomCount() {
    return Math.floor(Math.random() * 16) + 5; // 5-20
}

function generateQuestion() {
    // 生成两个不同的随机字母
    target1.char = generateRandomLetter();
    do {
        target2.char = generateRandomLetter();
    } while (target2.char === target1.char);
    
    // 生成随机数量
    target1.count = generateRandomCount();
    target2.count = generateRandomCount();
    
    // 更新显示
    document.getElementById('target1-char').textContent = target1.char;
    document.getElementById('target1-count').textContent = target1.count;
    document.getElementById('target2-char').textContent = target2.char;
    document.getElementById('target2-count').textContent = target2.count;
    
    // 清空输入框
    document.getElementById('input').value = '';
    document.getElementById('input').classList.remove('error');
    
    startTimer();
}

function countCharacter(text, char) {
    return (text.match(new RegExp(char, 'g')) || []).length;
}

function showError() {
    const input = document.getElementById('input');
    input.classList.add('error');
    setTimeout(() => {
        input.classList.remove('error');
        resetGame();
    }, 1000);
}

function checkAnswer() {
    clearInterval(timer);
    const input = document.getElementById('input');
    const text = input.value.toUpperCase();
    
    const count1 = countCharacter(text, target1.char);
    const count2 = countCharacter(text, target2.char);
    
    if (count1 === target1.count && count2 === target2.count) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第七关！');
            window.location.href = '../level8/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError();
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 