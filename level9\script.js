let questionsCompleted = 0;
let timer = null;
let timeLeft = 15;
let correctGroupIndex = 0;
let targetSum = 0;

function startTimer() {
    clearInterval(timer);
    timeLeft = 15;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomNumber() {
    return Math.floor(Math.random() * 9) + 1; // 1-9
}

function generateNumberGroup() {
    return [
        generateRandomNumber(),
        generateRandomNumber(),
        generateRandomNumber()
    ];
}

function generateQuestion() {
    const container = document.getElementById('numbers-container');
    container.innerHTML = '';
    
    // 生成6组数字
    const groups = [];
    const sums = new Set();
    
    // 先生成5组随机数字
    for (let i = 0; i < 5; i++) {
        const group = generateNumberGroup();
        const sum = group.reduce((a, b) => a + b, 0);
        groups.push({ numbers: group, sum: sum });
        sums.add(sum);
    }
    
    // 生成正确答案组，确保和不重复
    let correctGroup;
    do {
        correctGroup = generateNumberGroup();
        targetSum = correctGroup.reduce((a, b) => a + b, 0);
    } while (sums.has(targetSum));
    
    // 随机插入正确答案组
    correctGroupIndex = Math.floor(Math.random() * 6);
    groups.splice(correctGroupIndex, 0, { numbers: correctGroup, sum: targetSum });
    
    // 更新目标数字显示
    document.getElementById('target').textContent = targetSum;
    
    // 创建数字组元素
    groups.forEach((group, index) => {
        const element = document.createElement('div');
        element.className = 'number-group';
        element.dataset.index = index;
        
        group.numbers.forEach(num => {
            const numElement = document.createElement('div');
            numElement.className = 'number';
            numElement.textContent = num;
            element.appendChild(numElement);
        });
        
        element.addEventListener('click', handleGroupClick);
        container.appendChild(element);
    });
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleGroupClick(event) {
    const group = event.currentTarget;
    const index = parseInt(group.dataset.index);
    
    if (index === correctGroupIndex) {
        clearInterval(timer);
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第九关！');
            window.location.href = '../level10/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError(group);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 