<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第 1 关 - 数学计算小游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-content">
            <h1>数学计算小游戏</h1>
            <p>完成20个计算题进入下一关</p>
            <div class="progress">题目进度：<span id="progress">0</span>/20</div>
            <div class="timer">剩余时间：<span id="timer">30</span>秒</div>
            <div id="question" class="question"></div>
            <input type="number" id="answer" placeholder="请输入答案">
            <button onclick="checkAnswer()">提交答案</button>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>