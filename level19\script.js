let questionsCompleted = 0;
let timer = null;
let timeLeft = 6;
let correctClockIndex = 0;
let targetTime = null;

function startTimer() {
    clearInterval(timer);
    timeLeft = 6;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomTime() {
    const hours = Math.floor(Math.random() * 12);
    const minutes = Math.floor(Math.random() * 12) * 5; // 只生成5的倍数的分钟数
    return { hours, minutes };
}

function formatTime(hours, minutes) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

function calculateHandAngles(hours, minutes) {
    // 计算时针角度（考虑分钟的影响）
    const hourAngle = (hours % 12 + minutes / 60) * 30;
    // 计算分针角度
    const minuteAngle = minutes * 6;
    return { hourAngle, minuteAngle };
}

function createClock(hours, minutes) {
    const clock = document.createElement('div');
    clock.className = 'clock';
    
    const { hourAngle, minuteAngle } = calculateHandAngles(hours, minutes);
    
    // 创建时针
    const hourHand = document.createElement('div');
    hourHand.className = 'hand hour-hand';
    hourHand.style.transform = `rotate(${hourAngle}deg)`;
    
    // 创建分针
    const minuteHand = document.createElement('div');
    minuteHand.className = 'hand minute-hand';
    minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
    
    // 创建中心点
    const centerDot = document.createElement('div');
    centerDot.className = 'center-dot';
    
    clock.appendChild(hourHand);
    clock.appendChild(minuteHand);
    clock.appendChild(centerDot);
    
    return clock;
}

function generateQuestion() {
    const container = document.getElementById('clocks-container');
    container.innerHTML = '';
    
    // 生成目标时间
    targetTime = generateRandomTime();
    document.getElementById('target-time').textContent = 
        formatTime(targetTime.hours, targetTime.minutes);
    
    // 生成6个时钟
    correctClockIndex = Math.floor(Math.random() * 6);
    
    for (let i = 0; i < 6; i++) {
        let clock;
        if (i === correctClockIndex) {
            clock = createClock(targetTime.hours, targetTime.minutes);
        } else {
            // 生成不同的随机时间
            let randomTime;
            do {
                randomTime = generateRandomTime();
            } while (randomTime.hours === targetTime.hours && 
                    randomTime.minutes === targetTime.minutes);
            clock = createClock(randomTime.hours, randomTime.minutes);
        }
        
        clock.dataset.index = i;
        clock.addEventListener('click', handleClockClick);
        container.appendChild(clock);
    }
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleClockClick(event) {
    const clock = event.currentTarget;
    const index = parseInt(clock.dataset.index);
    
    // 移除其他时钟的选中状态
    document.querySelectorAll('.clock.selected').forEach(c => {
        c.classList.remove('selected');
    });
    
    // 添加当前时钟的选中状态
    clock.classList.add('selected');
    
    if (index === correctClockIndex) {
        clearInterval(timer);
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 8) {
            alert('恭喜你完成第十九关！');
            window.location.href = '../level20/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        clearInterval(timer);
        showError(clock);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 