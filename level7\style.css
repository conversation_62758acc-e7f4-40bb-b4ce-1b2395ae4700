body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.targets {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 20px 0;
}

.target {
    font-size: 24px;
}

.target-char {
    font-weight: bold;
    font-family: monospace;
}

textarea {
    width: 400px;
    height: 120px;
    padding: 10px;
    font-size: 18px;
    background: transparent;
    border: 2px solid white;
    color: white;
    resize: none;
    outline: none;
    font-family: monospace;
}

textarea.error {
    border-color: red;
    color: red;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 