let questionsCompleted = 0;
let timer = null;
let timeLeft = 5;
let correctIndex = 0;

const features = {
    parity: ['奇数', '偶数'],
    color: ['白色', '黄色'],
    style: ['无下划线', '下划线']
};

function startTimer() {
    clearInterval(timer);
    timeLeft = 5;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomNumber() {
    return Math.floor(Math.random() * 99) + 1; // 1-99
}

function generateQuestion() {
    const container = document.getElementById('numbers-container');
    container.innerHTML = '';
    
    // 生成目标特征
    const targetParity = Math.random() < 0.5;  // true为偶数
    const targetColor = Math.random() < 0.5;   // true为黄色
    const targetStyle = Math.random() < 0.5;   // true为斜体
    
    // 更新描述
    const description = `${features.parity[targetParity ? 1 : 0]} ${features.color[targetColor ? 1 : 0]} ${features.style[targetStyle ? 1 : 0]}`;
    document.getElementById('description').textContent = description;
    
    // 生成正确答案
    let correctNum;
    do {
        correctNum = generateRandomNumber();
    } while ((correctNum % 2 === 0) !== targetParity);

    // 创建正确答案对象
    const correctAnswer = {
        number: correctNum,
        isEven: correctNum % 2 === 0,
        isYellow: targetColor,
        isItalic: targetStyle
    };

    // 生成4个位置，随机选择一个放置正确答案
    correctIndex = Math.floor(Math.random() * 4);
    const answers = [];

    // 生成所有答案
    for (let i = 0; i < 4; i++) {
        if (i === correctIndex) {
            answers.push(correctAnswer);
        } else {
            // 生成随机答案
            let randomAnswer;
            do {
                const num = generateRandomNumber();
                randomAnswer = {
                    number: num,
                    isEven: num % 2 === 0,
                    isYellow: Math.random() < 0.5,
                    isItalic: Math.random() < 0.5
                };

                // 如果随机生成的答案与正确答案完全相同，随机修改一个特征
                if (randomAnswer.isEven === correctAnswer.isEven && 
                    randomAnswer.isYellow === correctAnswer.isYellow && 
                    randomAnswer.isItalic === correctAnswer.isItalic) {
                    
                    const featureToChange = Math.floor(Math.random() * 3) + 1;
                    switch (featureToChange) {
                        case 1: // 修改奇偶性
                            randomAnswer.number = randomAnswer.isEven ? 
                                num + 1 : // 偶数变奇数
                                (num === 99 ? 98 : num + 1); // 奇数变偶数
                            randomAnswer.isEven = !randomAnswer.isEven;
                            break;
                        case 2: // 修改颜色
                            randomAnswer.isYellow = !randomAnswer.isYellow;
                            break;
                        case 3: // 修改斜体
                            randomAnswer.isItalic = !randomAnswer.isItalic;
                            break;
                    }
                }
            } while (randomAnswer.isEven === correctAnswer.isEven && 
                    randomAnswer.isYellow === correctAnswer.isYellow && 
                    randomAnswer.isItalic === correctAnswer.isItalic);

            answers.push(randomAnswer);
        }
    }

    // 创建DOM元素
    answers.forEach((answer, index) => {
        const element = document.createElement('div');
        element.className = 'number';
        element.textContent = answer.number;
        element.dataset.index = index;
        
        if (answer.isYellow) element.classList.add('yellow');
        if (answer.isItalic) element.classList.add('italic');
        
        element.addEventListener('click', handleNumberClick);
        container.appendChild(element);
    });
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleNumberClick(event) {
    clearInterval(timer);
    const index = parseInt(event.target.dataset.index);
    
    if (index === correctIndex) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 15) {
            alert('恭喜你完成第十一关！');
            window.location.href = '../level12/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError(event.target);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 