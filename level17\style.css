body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.areas-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 20px;
}

.area {
    width: 250px;
    height: 350px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    background-color: rgba(255, 255, 255, 0.05);
}

.shape {
    width: 15px;
    height: 15px;
    background-color: white;
    position: absolute;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
}

.shape:hover {
    transform: scale(1.2);
    box-shadow: 0 0 5px white;
}

.shape.error {
    background-color: red;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 