let questionsCompleted = 0;
let timer = null;
let timeLeft = 6;
let targetString = '';
let gamePhase = 'waiting'; // 'waiting', 'showing', 'input'
let showingIndex = 0;

function generateRandomString() {
    const length = Math.floor(Math.random() * 16) + 25; // 25-40 characters
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
}

function startTimer() {
    clearInterval(timer);
    timeLeft = 6;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 3) {
            document.getElementById('timer').parentElement.classList.add('warning');
        }
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            checkAnswer();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    clearInterval(timer);
    timer = null;
    gamePhase = 'waiting';
    document.getElementById('answer').value = '';
    document.getElementById('answer').disabled = false;
    document.getElementById('submit-btn').disabled = false;
    document.getElementById('timer').parentElement.classList.remove('warning');
    generateQuestion();
}

function createCharElements(str) {
    const container = document.getElementById('target-string');
    container.innerHTML = '';
    
    for (let i = 0; i < str.length; i++) {
        const charElement = document.createElement('span');
        charElement.className = 'char';
        charElement.textContent = str[i];
        container.appendChild(charElement);
    }
}

function showCharactersSequentially() {
    gamePhase = 'showing';
    document.getElementById('instruction-text').textContent = '现在可以开始输入';
    document.getElementById('answer').disabled = false; // 允许玩家开始输入
    document.getElementById('submit-btn').disabled = true;
    
    const charElements = document.querySelectorAll('.char');
    showingIndex = 0;
    
    function showNextChar() {
        document.getElementById('answer').focus();
        if (showingIndex < charElements.length) {
            charElements[showingIndex].classList.add('show');
            showingIndex++;
            setTimeout(showNextChar, 750); // 每个字符显示0.75秒
        } else {
            // 所有字符显示完毕，开始倒计时
            gamePhase = 'input';
            document.getElementById('submit-btn').disabled = false;
            startTimer();
        }
    }
    
    showNextChar();
}

function generateQuestion() {
    gamePhase = 'waiting';
    document.getElementById('instruction-text').textContent = '准备开始...';
    document.getElementById('answer').value = '';
    document.getElementById('answer').disabled = true;
    document.getElementById('submit-btn').disabled = true;
    document.getElementById('timer').parentElement.classList.remove('warning');
    
    // 清除之前的字符显示
    document.getElementById('target-string').innerHTML = '';
    
    setTimeout(() => {
        targetString = generateRandomString();
        createCharElements(targetString);
        showCharactersSequentially();
    }, 1000);
}

function showError() {
    const input = document.getElementById('answer');
    input.classList.add('error');
    setTimeout(() => {
        input.classList.remove('error');
        resetGame();
    }, 1000);
}

function checkAnswer() {
    if (gamePhase !== 'input') return;
    
    clearInterval(timer);
    const input = document.getElementById('answer');
    const userAnswer = input.value.trim();
    
    if (userAnswer === targetString) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 5) {
            alert('恭喜你完成第二十二关！');
            window.location.href = '../level23/index.html';
            return;
        }
        
        document.getElementById('instruction-text').textContent = '正确！准备下一题...';
        document.getElementById('timer').parentElement.classList.remove('warning');
        setTimeout(() => {
            generateQuestion();
        }, 1500);
    } else {
        document.getElementById('instruction-text').textContent = `错误！正确答案是：${targetString}`;
        setTimeout(() => {
            showError();
        }, 2000);
    }
}

// 添加回车键监听
document.getElementById('answer').addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !document.getElementById('submit-btn').disabled) {
        checkAnswer();
    }
});

// 游戏开始时生成第一个问题
generateQuestion();
