body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.number-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    width: 420px;
    margin: 20px auto;
}

.number-cell {
    width: 60px;
    height: 60px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s;
    user-select: none;
}

.number-cell:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.number-cell.error {
    border-color: red;
    background-color: rgba(255, 0, 0, 0.2);
    animation: shake 0.5s;
    color: red;
}

.number-cell.correct {
    border-color: red;
    background-color: rgba(255, 0, 0, 0.2);
    color: red;
    animation: pulse 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.timer, .progress {
    font-size: 18px;
    margin: 10px 0;
} 