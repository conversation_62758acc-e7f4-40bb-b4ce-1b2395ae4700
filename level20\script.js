let questionsCompleted = 0;
let timer = null;
let timeLeft = 20;
let currentArrow = null;
let lastArrow = null;
const arrows = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];

function startTimer() {
    clearInterval(timer);
    timeLeft = 20;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    clearInterval(timer);  // 清除旧的计时器
    timer = null;  // 重置计时器变量
    generateQuestion();  // generateQuestion 会重新启动计时器
}

function generateQuestion() {
    // 移除之前的高亮
    if (currentArrow) {
        document.querySelector(`[data-key="${currentArrow}"]`).classList.remove('highlight');
    }
    
    // 随机选择一个新箭头，但不能和上一次相同
    let availableArrows = arrows.filter(arrow => arrow !== lastArrow);
    currentArrow = availableArrows[Math.floor(Math.random() * availableArrows.length)];
    lastArrow = currentArrow;
    
    // 添加高亮
    document.querySelector(`[data-key="${currentArrow}"]`).classList.add('highlight');
    
    if (!timer) {
        startTimer();
    }
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
        resetGame();
    }, 1000);
}

function handleKeyPress(event) {
    if (!timer) return; // 如果没有计时器在运行，忽略按键
    
    const pressedKey = event.key;
    const arrowElement = document.querySelector(`[data-key="${pressedKey}"]`);
    
    if (!arrowElement) return; // 如果不是箭头键，忽略
    
    if (pressedKey === currentArrow) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 50) {
            clearInterval(timer);
            alert('恭喜你完成第二十关！');
            window.location.href = '../level21/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        clearInterval(timer);
        showError(arrowElement);
    }
}

// 添加键盘事件监听
document.addEventListener('keydown', handleKeyPress);

// 添加点击事件监听
document.querySelectorAll('.arrow').forEach(arrow => {
    arrow.addEventListener('click', (event) => {
        const clickedKey = event.target.dataset.key;
        // 模拟键盘事件
        handleKeyPress({ key: clickedKey });
    });
});

// 游戏开始时生成第一个问题
generateQuestion(); 