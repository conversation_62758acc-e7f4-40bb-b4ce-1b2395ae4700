let questionsCompleted = 0;
let timer = null;
let timeLeft = 10;
let currentNumbers = [];
let duplicateNumber = null;
let isShowingNumbers = false;
let gamePhase = 'waiting'; // 'waiting', 'showing', 'input'

function startTimer() {
    clearInterval(timer);
    timeLeft = 10;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            if (gamePhase === 'input') {
                alert('时间到！游戏重新开始');
                resetGame();
            }
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    clearInterval(timer);
    timer = null;
    gamePhase = 'waiting';
    document.getElementById('answer').value = '';
    document.getElementById('submit-btn').disabled = false;
    generateQuestion();
}

function generateNumbers() {
    // 生成6-12个数字，其中一个重复
    const count = Math.floor(Math.random() * 7) + 6; // 6-12个数字
    const numbers = [];
    
    // 生成不重复的数字
    const usedNumbers = new Set();
    while (numbers.length < count - 1) {
        const num = Math.floor(Math.random() * 100); // 0-99的数字
        if (!usedNumbers.has(num)) {
            numbers.push(num);
            usedNumbers.add(num);
        }
    }
    
    // 随机选择一个数字作为重复数字
    duplicateNumber = numbers[Math.floor(Math.random() * numbers.length)];
    numbers.push(duplicateNumber);
    
    // 打乱数组
    for (let i = numbers.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numbers[i], numbers[j]] = [numbers[j], numbers[i]];
    }
    
    return numbers;
}

function createNumberElements(numbers) {
    const container = document.getElementById('numbers-container');
    container.innerHTML = '';
    
    numbers.forEach((num, index) => {
        const numberElement = document.createElement('div');
        numberElement.className = 'number';
        numberElement.textContent = num;
        numberElement.dataset.index = index;
        container.appendChild(numberElement);
    });
}

function showNumbersSequentially() {
    gamePhase = 'showing';
    isShowingNumbers = true;
    document.getElementById('instruction-text').textContent = '记住这些数字...';
    document.getElementById('submit-btn').disabled = true;
    
    const numberElements = document.querySelectorAll('.number');
    let currentIndex = 0;
    
    function showNextNumber() {
        if (currentIndex < numberElements.length) {
            // 隐藏之前的数字
            if (currentIndex > 0) {
                numberElements[currentIndex - 1].classList.remove('show');
            }
            
            // 显示当前数字
            numberElements[currentIndex].classList.add('show');
            currentIndex++;
            
            setTimeout(showNextNumber, 800); // 每个数字显示0.8秒
        } else {
            // 隐藏最后一个数字
            if (numberElements.length > 0) {
                numberElements[numberElements.length - 1].classList.remove('show');
            }
            
            // 数字显示完毕，开始输入阶段
            setTimeout(() => {
                isShowingNumbers = false;
                gamePhase = 'input';
                document.getElementById('instruction-text').textContent = '输入重复出现的数字';
                document.getElementById('submit-btn').disabled = false;
                document.getElementById('answer').focus();
                startTimer();
            }, 500);
        }
    }
    
    showNextNumber();
}

function generateQuestion() {
    gamePhase = 'waiting';
    document.getElementById('instruction-text').textContent = '准备开始...';
    document.getElementById('answer').value = '';
    document.getElementById('submit-btn').disabled = true;
    
    // 清除之前的数字显示
    const container = document.getElementById('numbers-container');
    container.innerHTML = '';
    
    setTimeout(() => {
        currentNumbers = generateNumbers();
        createNumberElements(currentNumbers);
        showNumbersSequentially();
    }, 1000);
}

function showError() {
    const input = document.getElementById('answer');
    input.classList.add('error');
    setTimeout(() => {
        input.classList.remove('error');
        resetGame();
    }, 1000);
}

function checkAnswer() {
    if (gamePhase !== 'input') return;
    
    const input = document.getElementById('answer');
    const userAnswer = parseInt(input.value);
    
    if (isNaN(userAnswer)) {
        alert('请输入一个有效的数字！');
        return;
    }
    
    if (userAnswer === duplicateNumber) {
        clearInterval(timer);
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 10) {
            alert('恭喜你完成第二十一关！恭喜你完成所有关卡！');
            return;
        }
        
        document.getElementById('instruction-text').textContent = '正确！准备下一题...';
        setTimeout(() => {
            generateQuestion();
        }, 1500);
    } else {
        clearInterval(timer);
        showError();
    }
}

// 添加回车键监听
document.getElementById('answer').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        checkAnswer();
    }
});

// 游戏开始时生成第一个问题
generateQuestion();
