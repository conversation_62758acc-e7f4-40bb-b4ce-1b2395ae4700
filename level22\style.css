body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.display-area {
    margin: 30px 0;
    min-height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.05);
    padding: 20px;
}

.target-string {
    font-size: 24px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    letter-spacing: 2px;
    word-break: break-all;
    line-height: 1.4;
    color: #00ff00;
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
    user-select: none;
}

.char {
    display: inline-block;
    opacity: 0;
    transition: opacity 0.3s;
}

.char.show {
    opacity: 1;
}

.input-section {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

#answer {
    padding: 10px;
    font-size: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: left;
    width: 300px;
    font-family: 'Courier New', monospace;
}

#answer:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

#answer:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#answer.error {
    border-color: red;
    background-color: rgba(255, 0, 0, 0.2);
    animation: shake 0.5s;
}

#submit-btn {
    padding: 10px 20px;
    font-size: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s;
}

#submit-btn:hover:not(:disabled) {
    border-color: #00ff00;
    background-color: rgba(0, 255, 0, 0.2);
}

#submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.instruction {
    margin-top: 20px;
    font-size: 16px;
    color: #ccc;
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
}

.timer.warning {
    color: #ff6666;
    animation: pulse 1s infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
