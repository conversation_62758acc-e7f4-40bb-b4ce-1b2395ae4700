let correctGroupIndex = 0;
let questionsCompleted = 0;
let timer = null;
let timeLeft = 20;
let targetNumber = 0;

const shapes = ['⚪', '▲', '■', '★', '♦', '♥'];

function startTimer() {
    clearInterval(timer);
    timeLeft = 20;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function isOverlapping(rect1, rect2) {
    return !(rect1.x + rect1.width <= rect2.x ||
             rect1.x >= rect2.x + rect2.width ||
             rect1.y + rect1.height <= rect2.y ||
             rect1.y >= rect2.y + rect2.height);
}

function generateRandomPosition(container, size) {
    const rect = container.getBoundingClientRect();
    const maxX = rect.width - size;
    const maxY = rect.height - size;
    const maxAttempts = 50;
    const existingShapes = Array.from(container.children).map(child => {
        return {
            x: parseInt(child.style.left),
            y: parseInt(child.style.top),
            width: size,
            height: size
        };
    });

    for (let i = 0; i < maxAttempts; i++) {
        const x = Math.floor(Math.random() * maxX);
        const y = Math.floor(Math.random() * maxY);
        const newRect = { x, y, width: size, height: size };
        
        let hasOverlap = false;
        for (const shape of existingShapes) {
            if (isOverlapping(newRect, shape)) {
                hasOverlap = true;
                break;
            }
        }
        
        if (!hasOverlap) {
            return { x, y };
        }
    }
    
    return null;
}

function createShape(container, shape) {
    const size = 20;
    const element = document.createElement('div');
    element.className = 'shape';
    element.textContent = shape;
    element.style.fontSize = '20px';
    element.style.width = size + 'px';
    element.style.height = size + 'px';
    element.style.lineHeight = size + 'px';
    
    const pos = generateRandomPosition(container, size);
    if (pos === null) {
        return null;
    }
    
    element.style.left = pos.x + 'px';
    element.style.top = pos.y + 'px';
    
    return element;
}

function generateShapeGroup(container, count, shape) {
    container.innerHTML = '';
    let placedShapes = 0;
    
    while (placedShapes < count) {
        const element = createShape(container, shape);
        if (element === null) break;
        container.appendChild(element);
        placedShapes++;
    }
}

function generateQuestion() {
    const groups = document.querySelectorAll('.shapes-container');
    targetNumber = Math.floor(Math.random() * 10) + 5; // 5-15的随机数
    document.getElementById('target').textContent = targetNumber;
    
    // 随机选择正确答案的组
    correctGroupIndex = Math.floor(Math.random() * 4);
    
    groups.forEach((container, index) => {
        const shape = shapes[Math.floor(Math.random() * shapes.length)];
        const count = index === correctGroupIndex ? 
            targetNumber : 
            targetNumber + (Math.random() < 0.5 ? 1 : -1) * (Math.floor(Math.random() * 3) + 1);
        generateShapeGroup(container, Math.max(count, 1), shape);
    });
    
    startTimer();
}

function showError(element) {
    element.classList.add('error');
    setTimeout(() => {
        element.classList.remove('error');
    }, 1000);
}

function handleGroupClick(event) {
    const group = event.currentTarget;
    const index = parseInt(group.dataset.index);
    
    if (index === correctGroupIndex) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 20) {
            clearInterval(timer);
            alert('恭喜你完成第三关！');
            window.location.href = '../level4/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError(group);
        resetGame();
    }
}

// 添加点击事件监听
document.querySelectorAll('.shape-group').forEach(group => {
    group.addEventListener('click', handleGroupClick);
});

// 游戏开始时生成第一个问题
generateQuestion(); 