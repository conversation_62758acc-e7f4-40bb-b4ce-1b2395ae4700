body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.shapes-container {
    width: 460px;
    height: 220px;
    margin: 15px auto;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.shape {
    position: absolute;
    text-align: center;
    user-select: none;
    font-size: 20px;
    width: 24px;
    height: 24px;
    line-height: 24px;
}

.answer-section {
    margin-top: 5px;
}

.answer-section.hidden {
    display: none;
}

.answer-section p {
    margin: 3px 0;
    font-size: 16px;
}

input {
    padding: 6px;
    font-size: 16px;
    width: 80px;
    margin: 3px;
    text-align: center;
    background: transparent;
    border: 2px solid white;
    color: white;
    outline: none;
}

input.error {
    border-color: red;
    color: red;
    animation: shake 0.5s;
}

button {
    padding: 6px 16px;
    font-size: 16px;
    background: transparent;
    color: white;
    border: 2px solid white;
    cursor: pointer;
    margin: 3px;
    transition: all 0.3s;
}

button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.memory-phase, .progress {
    font-size: 16px;
    margin: 8px 0;
}

.memory-phase.hidden {
    visibility: hidden;
} 