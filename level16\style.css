body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.numbers-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
    max-width: 400px;
    margin: 40px auto;
}

.number {
    width: 60px;
    height: 60px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-family: monospace;
    transition: all 0.3s;
}

.number.error {
    color: red;
    border-color: red;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 