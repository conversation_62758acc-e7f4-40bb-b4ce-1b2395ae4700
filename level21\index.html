<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第 21 关 - 数字记忆游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-content">
            <h1>数字记忆游戏</h1>
            <p>记住重复出现的数字</p>
            <div class="progress">完成次数：<span id="progress">0</span>/10</div>
            <div class="timer">剩余时间：<span id="timer">10</span>秒</div>
            <div id="numbers-container" class="numbers-container">
                <!-- 数字将由JavaScript动态生成 -->
            </div>
            <div class="input-section">
                <input type="number" id="answer" placeholder="输入重复的数字" min="0" max="99">
                <button id="submit-btn" onclick="checkAnswer()">提交</button>
            </div>
            <div class="instruction">
                <p id="instruction-text">准备开始...</p>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>
