let correctCellIndex = 0;
let questionsCompleted = 0;
let timer = null;
let timeLeft = 15;

function startTimer() {
    clearInterval(timer);
    timeLeft = 15;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    generateQuestion();
}

function generateRandomTwoDigitNumber() {
    return Math.floor(Math.random() * 90) + 10; // 10-99
}

function generateQuestion() {
    const grid = document.querySelector('.number-grid');
    grid.innerHTML = '';
    
    // 生成主要数字和不同的数字
    const mainNumber = generateRandomTwoDigitNumber();
    let differentNumber;
    do {
        differentNumber = generateRandomTwoDigitNumber();
    } while (differentNumber === mainNumber);
    
    // 随机选择一个位置放置不同的数字
    correctCellIndex = Math.floor(Math.random() * 36);
    
    // 创建36个单元格
    for (let i = 0; i < 36; i++) {
        const cell = document.createElement('div');
        cell.className = 'number-cell';
        cell.textContent = i === correctCellIndex ? differentNumber : mainNumber;
        cell.dataset.index = i;
        cell.addEventListener('click', handleCellClick);
        grid.appendChild(cell);
    }
    
    startTimer();
}

function showError(element) {
    // 显示点击错误的单元格
    element.classList.add('error');
    
    // 显示正确答案
    const correctCell = document.querySelector(`[data-index="${correctCellIndex}"]`);
    correctCell.classList.add('correct');
    
    setTimeout(() => {
        element.classList.remove('error');
        correctCell.classList.remove('correct');
        resetGame();
    }, 1000);
}

function handleCellClick(event) {
    const cell = event.currentTarget;
    const index = parseInt(cell.dataset.index);
    
    if (index === correctCellIndex) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 20) {
            clearInterval(timer);
            alert('恭喜你完成第四关！');
            window.location.href = '../level5/index.html';
            return;
        }
        
        generateQuestion();
    } else {
        showError(cell);
    }
}

// 游戏开始时生成第一个问题
generateQuestion(); 