body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.bars-container {
    width: 400px;
    margin: 40px auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.progress-bar {
    width: 100%;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    position: relative;
    cursor: pointer;
    transition: transform 0.3s;
}

.progress-bar:hover {
    transform: scale(1.05);
}

.bar-fill {
    width: 0;
    height: 100%;
    background-color: white;
    transition: width 0.1s linear;
}

.bar-label {
    position: absolute;
    right: -50px;
    top: 50%;
    transform: translateY(-50%);
}

.progress-bar.error {
    animation: shake 0.5s;
}

.progress-bar.error .bar-fill {
    background-color: red;
}

.replay-button {
    padding: 10px 20px;
    font-size: 18px;
    background: transparent;
    color: white;
    border: 2px solid white;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 20px;
}

.replay-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.replay-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.progress {
    font-size: 16px;
    margin: 8px 0;
} 