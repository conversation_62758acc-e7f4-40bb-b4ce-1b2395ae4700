body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.numbers-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    margin: 30px 0;
    min-height: 200px;
    align-items: center;
    justify-items: center;
}

.number {
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s;
}

.number.show {
    opacity: 1;
    border-color: #00ff00;
    background-color: rgba(0, 255, 0, 0.2);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.input-section {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

#answer {
    padding: 10px;
    font-size: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: center;
    width: 150px;
}

#answer:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

#answer.error {
    border-color: red;
    background-color: rgba(255, 0, 0, 0.2);
    animation: shake 0.5s;
}

#submit-btn {
    padding: 10px 20px;
    font-size: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s;
}

#submit-btn:hover {
    border-color: #00ff00;
    background-color: rgba(0, 255, 0, 0.2);
}

#submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.instruction {
    margin-top: 20px;
    font-size: 16px;
    color: #ccc;
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
