let correctAnswer = 0;
let questionsCompleted = 0;
let timer = null;
let timeLeft = 20;

const shapes = ['⚪', '▲', '■'];
const shapeNames = {
    '⚪': '圆形',
    '▲': '三角形',
    '■': '方形'
};

function startTimer() {
    clearInterval(timer);
    timeLeft = 20;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    document.getElementById('answer').value = '';
    generateQuestion();
}

function isOverlapping(rect1, rect2) {
    return !(rect1.x + rect1.width <= rect2.x ||
             rect1.x >= rect2.x + rect2.width ||
             rect1.y + rect1.height <= rect2.y ||
             rect1.y >= rect2.y + rect2.height);
}

function generateRandomPosition(size) {
    const container = document.getElementById('shapes-container');
    const maxX = container.offsetWidth - size;
    const maxY = container.offsetHeight - size;
    const maxAttempts = 50;
    const existingShapes = Array.from(container.children).map(child => {
        return {
            x: parseInt(child.style.left),
            y: parseInt(child.style.top),
            width: size,
            height: size
        };
    });

    for (let i = 0; i < maxAttempts; i++) {
        const x = Math.floor(Math.random() * maxX);
        const y = Math.floor(Math.random() * maxY);
        const newRect = { x, y, width: size, height: size };
        
        // 检查是否与现有图形重叠
        let hasOverlap = false;
        for (const shape of existingShapes) {
            if (isOverlapping(newRect, shape)) {
                hasOverlap = true;
                break;
            }
        }
        
        if (!hasOverlap) {
            return { x, y };
        }
    }
    
    // 如果尝试多次仍找不到位置，返回null
    return null;
}

function createShape(shape) {
    const size = 20; // 减小图形大小
    const element = document.createElement('div');
    element.className = 'shape';
    element.textContent = shape;
    element.style.fontSize = '20px'; // 减小字体大小
    element.style.width = size + 'px';
    element.style.height = size + 'px';
    element.style.lineHeight = size + 'px';
    
    const pos = generateRandomPosition(size);
    if (pos === null) {
        return null; // 如果找不到合适的位置，返回null
    }
    
    element.style.left = pos.x + 'px';
    element.style.top = pos.y + 'px';
    
    return element;
}

function generateQuestion() {
    const container = document.getElementById('shapes-container');
    container.innerHTML = '';
    
    const totalShapes = Math.floor(Math.random() * 20) + 10; // 10-30个图形
    const targetShape = shapes[Math.floor(Math.random() * shapes.length)];
    let count = 0;
    let placedShapes = 0;
    
    // 尝试放置图形，直到达到目标数量或无法继续放置
    while (placedShapes < totalShapes) {
        const shape = shapes[Math.floor(Math.random() * shapes.length)];
        const element = createShape(shape);
        
        if (element === null) {
            break; // 如果无法放置更多图形，则退出循环
        }
        
        if (shape === targetShape) count++;
        container.appendChild(element);
        placedShapes++;
    }
    
    document.getElementById('target-shape').textContent = shapeNames[targetShape];
    correctAnswer = count;
    startTimer();
}

function showError() {
    const input = document.getElementById('answer');
    input.classList.add('error');
    setTimeout(() => {
        input.classList.remove('error');
    }, 1000);
}

function checkAnswer() {
    const input = document.getElementById('answer');
    const userAnswer = parseInt(input.value);
    
    if (userAnswer === correctAnswer) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 20) {
            clearInterval(timer);
            alert('恭喜你完成第二关！');
            window.location.href = '../level3/index.html';
            return;
        }
        
        input.value = '';
        generateQuestion();
    } else {
        showError();
        input.value = '';
    }
}

// 添加回车键监听
document.getElementById('answer').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        checkAnswer();
    }
});

// 游戏开始时生成第一个问题
generateQuestion(); 