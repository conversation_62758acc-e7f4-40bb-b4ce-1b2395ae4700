let currentQuestion = '';
let correctAnswer = 0;
let questionsCompleted = 0;
let timer = null;
let timeLeft = 30;

function startTimer() {
    clearInterval(timer);
    timeLeft = 30;
    updateTimerDisplay();
    
    timer = setInterval(() => {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            alert('时间到！游戏重新开始');
            resetGame();
        }
    }, 1000);
}

function updateTimerDisplay() {
    document.getElementById('timer').textContent = timeLeft;
}

function resetGame() {
    questionsCompleted = 0;
    document.getElementById('progress').textContent = '0';
    document.getElementById('answer').value = '';
    generateQuestion();
}

function generateQuestion() {
    const num1 = Math.floor(Math.random() * 100);
    const num2 = Math.floor(Math.random() * 100);
    const operation = Math.random() < 0.5 ? '+' : '-';
    
    if (operation === '+') {
        if (num1 + num2 > 500) {
            return generateQuestion();
        }
        correctAnswer = num1 + num2;
        currentQuestion = `${num1} + ${num2} = ?`;
    } else {
        if (num1 < num2) {
            return generateQuestion();
        }
        correctAnswer = num1 - num2;
        currentQuestion = `${num1} - ${num2} = ?`;
    }
    
    document.getElementById('question').textContent = currentQuestion;
    startTimer();
}

function showError() {
    const input = document.getElementById('answer');
    input.classList.add('error');
    setTimeout(() => {
        input.classList.remove('error');
    }, 1000);
}

function checkAnswer() {
    const input = document.getElementById('answer');
    const userAnswer = parseInt(input.value);
    
    if (userAnswer === correctAnswer) {
        questionsCompleted++;
        document.getElementById('progress').textContent = questionsCompleted;
        
        if (questionsCompleted >= 20) {
            clearInterval(timer);
            alert('恭喜你完成第一关！');
            window.location.href = '../level2/index.html';
            return;
        }
        
        input.value = '';
        generateQuestion();
    } else {
        showError();
        input.value = '';
    }
}

// 添加回车键监听
document.getElementById('answer').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        checkAnswer();
    }
});

// 游戏开始时生成第一个问题
generateQuestion(); 
generateQuestion(); 