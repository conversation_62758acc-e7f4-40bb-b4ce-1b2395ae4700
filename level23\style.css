body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 8px;
    width: 320px;
    height: 320px;
    margin: 30px auto;
    padding: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.5);
}

.grid-cell {
    background-color: white;
    cursor: pointer;
    transition: all 0.3s;
    border: 2px solid transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: black;
    user-select: none;
}

.grid-cell:hover {
    border-color: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.grid-cell.clicked {
    border-color: #00ff00;
    background-color: rgba(0, 255, 0, 0.3) !important;
    color: white;
    cursor: not-allowed;
}

.grid-cell.error {
    border-color: red;
    background-color: rgba(255, 0, 0, 0.5) !important;
    animation: shake 0.5s;
}

.progress {
    font-size: 16px;
    margin: 8px 0;
}

.instruction {
    margin-top: 20px;
    font-size: 16px;
    color: #ccc;
}

.instruction.success {
    color: #00ff00;
    font-weight: bold;
}

.instruction.error {
    color: #ff6666;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
