body {
    margin: 0;
    padding: 0;
    background-color: black;
    color: white;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.game-container {
    width: 600px;
    height: 600px;
    border: 2px solid white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.grid-container {
    width: 300px;
    height: 300px;
    margin: 20px auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 10px;
}

.number-cell {
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-family: monospace;
    cursor: pointer;
    transition: all 0.3s;
}

.number-cell:hover {
    transform: scale(1.1);
    border-color: white;
}

.number-cell.selected {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: white;
}

.number-cell.error {
    border-color: red;
    color: red;
    animation: shake 0.5s;
}

.status {
    font-size: 18px;
    margin-top: 20px;
    min-height: 27px;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.timer, .progress {
    font-size: 16px;
    margin: 8px 0;
} 